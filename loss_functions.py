#!/usr/bin/env python3
"""
Optimized Loss Functions for SVDNet

This module implements simplified and efficient loss functions for the optimized SVDNet.
Key improvements:
- Simplified orthogonality constraints
- Reduced computational overhead
- Better numerical stability
- Direct alignment with AE metric
"""

import torch
import torch.nn as nn
import torch.nn.functional as F


def to_complex(t):
    """Convert real-imaginary tensor to complex."""
    return torch.complex(t[..., 0], t[..., 1])


def frobenius_norm_squared(t):
    """Compute squared Frobenius norm for complex tensor."""
    return torch.sum(torch.abs(t) ** 2, dim=(-2, -1))


# -----------------------------------------------------------------------------
# Optimized Loss Functions
# -----------------------------------------------------------------------------

class OptimizedLoss(nn.Module):
    """
    Optimized loss function with simplified orthogonality constraints.

    Key improvements:
    - Simplified orthogonality loss using column normalization
    - Reduced computational complexity
    - Better numerical stability
    - Direct optimization for AE metric
    """

    def __init__(self, lambda_rec=1.0, lambda_orth=0.01, eps=1e-8):
        super().__init__()
        self.lambda_rec = lambda_rec
        self.lambda_orth = lambda_orth
        self.eps = eps

    def forward(self, U, S, V, H_gt):
        """
        Compute optimized loss with simplified constraints.

        Args:
            U: Left unitary matrix [B, M, R, 2]
            S: Singular values [B, R]
            V: Right unitary matrix [B, N, R, 2]
            H_gt: Ground truth channel [B, M, N, 2]

        Returns:
            Dictionary containing loss components
        """
        # Convert to complex tensors
        U_c = to_complex(U)
        V_c = to_complex(V)
        S_c = S.type(torch.complex64)
        H_gt_c = to_complex(H_gt)

        # 1. Reconstruction loss (primary objective)
        recon_loss = self._reconstruction_loss(U_c, S_c, V_c, H_gt_c)

        # 2. Simplified orthogonality loss (column normalization)
        orth_loss = self._simplified_orthogonality_loss(U_c, V_c)

        # 3. Total loss
        total_loss = self.lambda_rec * recon_loss + self.lambda_orth * orth_loss

        return {
            'total_loss': total_loss,
            'reconstruction_loss': recon_loss,
            'orthogonality_loss': orth_loss
        }

    def _reconstruction_loss(self, U_c, S_c, V_c, H_gt_c):
        """Compute normalized reconstruction error."""
        # Reconstruct: H_pred = U @ diag(S) @ V^H
        U_S = U_c * S_c.unsqueeze(1)  # Broadcasting
        H_pred = torch.matmul(U_S, torch.conj(V_c).transpose(-2, -1))

        # Normalized reconstruction error
        diff = H_pred - H_gt_c
        numerator = torch.sum(torch.abs(diff) ** 2, dim=(-2, -1))
        denominator = torch.sum(torch.abs(H_gt_c) ** 2, dim=(-2, -1)) + self.eps

        normalized_error = numerator / denominator
        return torch.mean(normalized_error)

    def _simplified_orthogonality_loss(self, U_c, V_c):
        """Simplified orthogonality loss using column normalization."""
        # Column norm penalty - encourage unit length columns
        U_norms = torch.norm(U_c, dim=1)  # [B, R]
        V_norms = torch.norm(V_c, dim=1)  # [B, R]

        U_norm_loss = torch.mean((U_norms - 1.0) ** 2)
        V_norm_loss = torch.mean((V_norms - 1.0) ** 2)

        return U_norm_loss + V_norm_loss


class FastAELoss(nn.Module):
    """
    Fast approximation of AE metric for efficient training.

    Uses simplified orthogonality constraints while maintaining
    alignment with the competition's AE metric.
    """

    def __init__(self, lambda_rec=1.0, lambda_orth_u=0.05, lambda_orth_v=0.05):
        super().__init__()
        self.lambda_rec = lambda_rec
        self.lambda_orth_u = lambda_orth_u
        self.lambda_orth_v = lambda_orth_v

    def forward(self, U, S, V, H_gt):
        """Fast AE-aligned loss computation."""
        # Convert to complex
        U_c = to_complex(U)
        V_c = to_complex(V)
        H_gt_c = to_complex(H_gt)

        # Reconstruction loss
        H_pred = torch.matmul(U_c * S.unsqueeze(1),
                             torch.conj(V_c).transpose(-2, -1))
        recon_error = torch.mean(torch.abs(H_pred - H_gt_c) ** 2)

        # Fast orthogonality approximation
        # Instead of computing U^H @ U, just penalize deviation from unit norms
        U_norms = torch.norm(U_c, dim=1)
        V_norms = torch.norm(V_c, dim=1)

        orth_loss_u = torch.mean((U_norms - 1.0) ** 2)
        orth_loss_v = torch.mean((V_norms - 1.0) ** 2)

        total_loss = (self.lambda_rec * recon_error +
                     self.lambda_orth_u * orth_loss_u +
                     self.lambda_orth_v * orth_loss_v)

        return {
            'total_loss': total_loss,
            'reconstruction_loss': recon_error,
            'orthogonality_loss_u': orth_loss_u,
            'orthogonality_loss_v': orth_loss_v
        }


class AEAlignedLoss(nn.Module):
    """
    Multi-component loss function precisely aligned with AE metric.
    
    The AE metric consists of three components:
    1. Normalized reconstruction error
    2. Left singular matrix orthogonality error  
    3. Right singular matrix orthogonality error
    """
    
    def __init__(self, lambda_rec=1.0, lambda_orth_u=0.1, lambda_orth_v=0.1, 
                 eps=1e-8, use_normalized_orth=True):
        super().__init__()
        self.lambda_rec = lambda_rec
        self.lambda_orth_u = lambda_orth_u
        self.lambda_orth_v = lambda_orth_v
        self.eps = eps
        self.use_normalized_orth = use_normalized_orth
    
    def forward(self, U, S, V, H_gt):
        """
        Compute multi-component loss aligned with AE metric.
        
        Args:
            U: Left unitary matrix [B, M, R, 2]
            S: Singular values [B, R]
            V: Right unitary matrix [B, N, R, 2]
            H_gt: Ground truth channel [B, M, N, 2]
            
        Returns:
            Dictionary containing individual loss components and total loss
        """
        # Convert to complex tensors
        U_c = to_complex(U)
        V_c = to_complex(V)
        S_c = S.type(torch.complex64)
        H_gt_c = to_complex(H_gt)
        
        # 1. Normalized reconstruction error (primary component)
        recon_loss = self._reconstruction_loss(U_c, S_c, V_c, H_gt_c)
        
        # 2. Orthogonality losses (auxiliary regularization)
        orth_loss_u = self._orthogonality_loss(U_c, "U")
        orth_loss_v = self._orthogonality_loss(V_c, "V")
        
        # 3. Total weighted loss
        total_loss = (self.lambda_rec * recon_loss + 
                     self.lambda_orth_u * orth_loss_u + 
                     self.lambda_orth_v * orth_loss_v)
        
        return {
            'total_loss': total_loss,
            'reconstruction_loss': recon_loss,
            'orthogonality_loss_u': orth_loss_u,
            'orthogonality_loss_v': orth_loss_v,
            'ae_approximation': recon_loss + orth_loss_u + orth_loss_v  # Direct AE approximation
        }
    
    def _reconstruction_loss(self, U_c, S_c, V_c, H_gt_c):
        """
        Compute normalized reconstruction error exactly as in AE metric.
        
        AE_recon = ||H_label - U*S*V^H||_F / ||H_label||_F
        """
        # Reconstruct channel: H_pred = U @ diag(S) @ V^H
        U_S = U_c * S_c.unsqueeze(1)  # Broadcasting singular values
        H_pred = torch.matmul(U_S, torch.conj(V_c).transpose(-2, -1))
        
        # Compute normalized reconstruction error
        diff = H_pred - H_gt_c
        numerator = frobenius_norm_squared(diff)  # [B]
        denominator = frobenius_norm_squared(H_gt_c) + self.eps  # [B]
        
        normalized_error = numerator / denominator
        return torch.mean(normalized_error)
    
    def _orthogonality_loss(self, matrix, name):
        """
        Compute orthogonality loss: ||U^H @ U - I||_F^2
        
        Args:
            matrix: Unitary matrix [B, M, R] (complex)
            name: Matrix name for debugging
        """
        # Compute U^H @ U
        matrix_H = torch.conj(matrix).transpose(-2, -1)
        product = torch.matmul(matrix_H, matrix)  # [B, R, R]
        
        # Create identity matrix
        R = matrix.size(-1)
        I = torch.eye(R, device=matrix.device, dtype=matrix.dtype)
        I = I.expand_as(product)
        
        # Compute ||U^H @ U - I||_F^2
        diff = product - I
        orth_error = torch.mean(torch.abs(diff) ** 2, dim=(-2, -1))  # [B]
        
        if self.use_normalized_orth:
            # Normalize by matrix size for stability
            orth_error = orth_error / (R * R)
        
        return torch.mean(orth_error)


class AdaptiveLoss(nn.Module):
    """
    Adaptive loss function that adjusts weights based on training progress.
    """
    
    def __init__(self, initial_lambda_rec=1.0, initial_lambda_orth_u=0.1, 
                 initial_lambda_orth_v=0.1, adaptation_rate=0.01):
        super().__init__()
        self.base_loss = AEAlignedLoss(initial_lambda_rec, initial_lambda_orth_u, initial_lambda_orth_v)
        self.adaptation_rate = adaptation_rate
        self.step_count = 0
        
        # Learnable weight parameters
        self.log_lambda_orth_u = nn.Parameter(torch.log(torch.tensor(initial_lambda_orth_u)))
        self.log_lambda_orth_v = nn.Parameter(torch.log(torch.tensor(initial_lambda_orth_v)))
    
    def forward(self, U, S, V, H_gt):
        """Forward pass with adaptive weights."""
        self.step_count += 1
        
        # Update adaptive weights
        self.base_loss.lambda_orth_u = torch.exp(self.log_lambda_orth_u)
        self.base_loss.lambda_orth_v = torch.exp(self.log_lambda_orth_v)
        
        return self.base_loss(U, S, V, H_gt)


class RobustLoss(nn.Module):
    """
    Robust loss function with Huber-like behavior for outlier resistance.
    """
    
    def __init__(self, lambda_rec=1.0, lambda_orth_u=0.1, lambda_orth_v=0.1, 
                 huber_delta=1.0):
        super().__init__()
        self.lambda_rec = lambda_rec
        self.lambda_orth_u = lambda_orth_u
        self.lambda_orth_v = lambda_orth_v
        self.huber_delta = huber_delta
    
    def forward(self, U, S, V, H_gt):
        """Forward pass with robust loss."""
        # Get standard loss components
        base_loss = AEAlignedLoss(self.lambda_rec, self.lambda_orth_u, self.lambda_orth_v)
        loss_dict = base_loss(U, S, V, H_gt)
        
        # Apply Huber-like robustness to reconstruction loss
        recon_loss = loss_dict['reconstruction_loss']
        if recon_loss > self.huber_delta:
            robust_recon_loss = self.huber_delta * (2 * torch.sqrt(recon_loss / self.huber_delta) - 1)
        else:
            robust_recon_loss = recon_loss
        
        # Recompute total loss with robust reconstruction term
        total_loss = (self.lambda_rec * robust_recon_loss + 
                     self.lambda_orth_u * loss_dict['orthogonality_loss_u'] + 
                     self.lambda_orth_v * loss_dict['orthogonality_loss_v'])
        
        loss_dict['total_loss'] = total_loss
        loss_dict['robust_reconstruction_loss'] = robust_recon_loss
        
        return loss_dict


def compute_ae_metric(U, S, V, H_gt):
    """
    Compute the exact AE metric as defined in the competition.
    
    AE = ||H_label - U*S*V^H||_F / ||H_label||_F + ||U^H*U - I||_F + ||V^H*V - I||_F
    
    Args:
        U: Left unitary matrix [B, M, R, 2] or [M, R, 2]
        S: Singular values [B, R] or [R]
        V: Right unitary matrix [B, N, R, 2] or [N, R, 2]
        H_gt: Ground truth channel [B, M, N, 2] or [M, N, 2]
        
    Returns:
        AE metric value
    """
    # Ensure batch dimension
    if U.ndim == 3:
        U = U.unsqueeze(0)
        S = S.unsqueeze(0)
        V = V.unsqueeze(0)
        H_gt = H_gt.unsqueeze(0)
    
    # Convert to complex
    U_c = to_complex(U)
    V_c = to_complex(V)
    S_c = S.type(torch.complex64)
    H_gt_c = to_complex(H_gt)
    
    # 1. Normalized reconstruction error
    U_S = U_c * S_c.unsqueeze(1)
    H_pred = torch.matmul(U_S, torch.conj(V_c).transpose(-2, -1))
    
    recon_error = torch.norm(H_pred - H_gt_c, p='fro', dim=(-2, -1))
    h_norm = torch.norm(H_gt_c, p='fro', dim=(-2, -1))
    normalized_recon_error = recon_error / (h_norm + 1e-8)
    
    # 2. U orthogonality error
    U_H = torch.conj(U_c).transpose(-2, -1)
    UHU = torch.matmul(U_H, U_c)
    I_U = torch.eye(U_c.size(-1), device=U_c.device, dtype=U_c.dtype)
    orth_error_U = torch.norm(UHU - I_U, p='fro', dim=(-2, -1))
    
    # 3. V orthogonality error
    V_H = torch.conj(V_c).transpose(-2, -1)
    VHV = torch.matmul(V_H, V_c)
    I_V = torch.eye(V_c.size(-1), device=V_c.device, dtype=V_c.dtype)
    orth_error_V = torch.norm(VHV - I_V, p='fro', dim=(-2, -1))
    
    # Total AE
    ae = normalized_recon_error + orth_error_U + orth_error_V
    
    return torch.mean(ae)


if __name__ == "__main__":
    # Test the loss functions
    device = "cuda" if torch.cuda.is_available() else "cpu"
    
    # Create test data
    B, M, N, R = 4, 64, 64, 32
    U = torch.randn(B, M, R, 2, device=device)
    S = torch.rand(B, R, device=device)
    V = torch.randn(B, N, R, 2, device=device)
    H_gt = torch.randn(B, M, N, 2, device=device)
    
    # Test AE-aligned loss
    loss_fn = AEAlignedLoss().to(device)
    loss_dict = loss_fn(U, S, V, H_gt)
    
    print("Loss components:")
    for key, value in loss_dict.items():
        print(f"  {key}: {value.item():.6f}")
    
    # Test AE metric computation
    ae_value = compute_ae_metric(U, S, V, H_gt)
    print(f"\nAE metric: {ae_value.item():.6f}")
