import os
import torch
import torch.nn as nn
import torch.nn.functional as F

# -----------------------------------------------------------------------------
# Lightweight MobileNet-style Blocks for Optimized SVDNet
# -----------------------------------------------------------------------------

class DepthwiseSeparableConv(nn.Module):
    """Depthwise Separable Convolution for lightweight feature extraction."""

    def __init__(self, in_channels, out_channels, kernel_size=3, stride=1, padding=1):
        super().__init__()
        # Depthwise convolution
        self.depthwise = nn.Conv2d(
            in_channels, in_channels, kernel_size=kernel_size,
            stride=stride, padding=padding, groups=in_channels, bias=False
        )
        self.bn1 = nn.BatchNorm2d(in_channels)

        # Pointwise convolution
        self.pointwise = nn.Conv2d(in_channels, out_channels, kernel_size=1, bias=False)
        self.bn2 = nn.BatchNorm2d(out_channels)

        self.relu = nn.ReLU(inplace=True)

    def forward(self, x):
        x = self.depthwise(x)
        x = self.bn1(x)
        x = self.relu(x)

        x = self.pointwise(x)
        x = self.bn2(x)
        x = self.relu(x)

        return x


class LightweightBlock(nn.Module):
    """Lightweight building block with residual connection."""

    def __init__(self, in_channels, out_channels, stride=1, expand_ratio=2):
        super().__init__()
        self.use_residual = stride == 1 and in_channels == out_channels
        hidden_dim = in_channels * expand_ratio

        layers = []

        # Expansion
        if expand_ratio != 1:
            layers.extend([
                nn.Conv2d(in_channels, hidden_dim, 1, bias=False),
                nn.BatchNorm2d(hidden_dim),
                nn.ReLU(inplace=True)
            ])

        # Depthwise
        layers.extend([
            nn.Conv2d(hidden_dim, hidden_dim, 3, stride=stride,
                     padding=1, groups=hidden_dim, bias=False),
            nn.BatchNorm2d(hidden_dim),
            nn.ReLU(inplace=True)
        ])

        # Pointwise
        layers.extend([
            nn.Conv2d(hidden_dim, out_channels, 1, bias=False),
            nn.BatchNorm2d(out_channels)
        ])

        self.conv = nn.Sequential(*layers)

    def forward(self, x):
        if self.use_residual:
            return x + self.conv(x)
        else:
            return self.conv(x)


# -----------------------------------------------------------------------------
# Lightweight Feature Extractor Implementation
# -----------------------------------------------------------------------------

class LightweightFeatureExtractor(nn.Module):
    """Lightweight feature extractor using MobileNet-style architecture."""

    def __init__(self, in_channels=2):
        super().__init__()

        # Initial convolution
        self.stem = nn.Sequential(
            nn.Conv2d(in_channels, 32, kernel_size=3, stride=1, padding=1, bias=False),
            nn.BatchNorm2d(32),
            nn.ReLU(inplace=True)
        )

        # Lightweight blocks with progressive downsampling
        self.blocks = nn.Sequential(
            LightweightBlock(32, 64, stride=2, expand_ratio=2),   # 32x32
            LightweightBlock(64, 64, stride=1, expand_ratio=2),

            LightweightBlock(64, 128, stride=2, expand_ratio=2),  # 16x16
            LightweightBlock(128, 128, stride=1, expand_ratio=2),

            LightweightBlock(128, 256, stride=2, expand_ratio=2), # 8x8
            LightweightBlock(256, 256, stride=1, expand_ratio=2),
        )

        # Global average pooling
        self.global_pool = nn.AdaptiveAvgPool2d(1)

    def forward(self, x):
        """
        Args:
            x: Input tensor [B, 2, H, W]
        Returns:
            Global feature vector [B, 256]
        """
        x = self.stem(x)
        x = self.blocks(x)
        x = self.global_pool(x)
        x = x.flatten(1)  # [B, 256]
        return x


# -----------------------------------------------------------------------------
# Simplified Orthogonality Constraint Methods
# -----------------------------------------------------------------------------

def gram_schmidt_orthogonalization(matrix):
    """
    Apply Gram-Schmidt orthogonalization to ensure orthogonal columns.

    Args:
        matrix: Complex tensor [B, M, R] where R columns need to be orthogonalized
    Returns:
        Orthogonalized matrix [B, M, R]
    """
    batch_size, m, r = matrix.shape
    device = matrix.device
    dtype = matrix.dtype

    # Initialize output matrix
    orthogonal_matrix = torch.zeros_like(matrix)

    for i in range(r):
        # Current vector
        v = matrix[:, :, i].clone()  # [B, M]

        # Subtract projections onto previous orthogonal vectors
        for j in range(i):
            u_j = orthogonal_matrix[:, :, j]  # [B, M]
            # Compute projection: proj = <v, u_j> * u_j
            inner_product = torch.sum(torch.conj(u_j) * v, dim=1, keepdim=True)  # [B, 1]
            projection = inner_product * u_j  # [B, M]
            v = v - projection

        # Normalize the vector
        norm = torch.norm(v, dim=1, keepdim=True)  # [B, 1]
        orthogonal_matrix[:, :, i] = v / (norm + 1e-8)

    return orthogonal_matrix


def normalize_columns(matrix):
    """
    Normalize columns of a matrix to unit length.

    Args:
        matrix: Complex tensor [B, M, R]
    Returns:
        Normalized matrix [B, M, R]
    """
    # Compute column norms
    norms = torch.norm(matrix, dim=1, keepdim=True)  # [B, 1, R]

    # Normalize
    normalized_matrix = matrix / (norms + 1e-8)

    return normalized_matrix


def apply_orthogonality_constraint(matrix_ri, method='normalize'):
    """
    Apply orthogonality constraint to real-imaginary format matrix.

    Args:
        matrix_ri: Real-imaginary tensor [B, M, R, 2]
        method: 'normalize' or 'gram_schmidt'
    Returns:
        Constrained matrix [B, M, R, 2]
    """
    # Convert to complex
    matrix_c = torch.complex(matrix_ri[..., 0], matrix_ri[..., 1])

    if method == 'gram_schmidt':
        # Apply Gram-Schmidt orthogonalization
        constrained_c = gram_schmidt_orthogonalization(matrix_c)
    elif method == 'normalize':
        # Simple column normalization
        constrained_c = normalize_columns(matrix_c)
    else:
        raise ValueError(f"Unknown method: {method}")

    # Convert back to real-imaginary format
    constrained_ri = torch.stack([constrained_c.real, constrained_c.imag], dim=-1)

    return constrained_ri


# -----------------------------------------------------------------------------
# Optimized SVDNet Implementation
# -----------------------------------------------------------------------------


class OptimizedSVDNet(nn.Module):
    """
    Optimized lightweight SVDNet with direct matrix prediction and simplified constraints.

    Key improvements:
    - Lightweight MobileNet-style feature extractor (2-3M parameters vs 30M)
    - Direct matrix prediction without complex Cayley transforms
    - Simplified orthogonality constraints using Gram-Schmidt or normalization
    - Significantly reduced computational complexity
    """

    def __init__(self, dim=64, rank=32, weight_path="svdnet_optimized.pth",
                 orthogonality_method='normalize'):
        super().__init__()
        self.dim = dim
        self.rank = rank
        self.orthogonality_method = orthogonality_method

        # Lightweight feature extractor
        self.feature_extractor = LightweightFeatureExtractor(in_channels=2)

        # Shared feature processing
        feature_dim = 256  # Output from feature extractor
        hidden_dim = 512

        self.shared_fc = nn.Sequential(
            nn.Linear(feature_dim, hidden_dim),
            nn.ReLU(inplace=True),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(inplace=True),
            nn.Dropout(0.1)
        )

        # Direct matrix prediction heads
        self.head_U = nn.Sequential(
            nn.Linear(hidden_dim, dim * rank * 2),  # Direct U matrix prediction
            nn.Tanh()  # Bounded output for stability
        )

        self.head_V = nn.Sequential(
            nn.Linear(hidden_dim, dim * rank * 2),  # Direct V matrix prediction
            nn.Tanh()  # Bounded output for stability
        )

        self.head_S = nn.Sequential(
            nn.Linear(hidden_dim, rank),
            nn.Softplus()  # Ensure positive singular values
        )

        # Initialize weights
        self._initialize_weights()

        # Load pretrained weights if available
        if os.path.isfile(weight_path):
            try:
                state_dict = torch.load(weight_path, map_location="cpu")
                self.load_state_dict(state_dict, strict=False)
                print(f"[OptimizedSVDNet] Loaded weights from {weight_path}")
            except Exception as e:
                print(f"[OptimizedSVDNet] Failed to load weights: {e}")

    def _initialize_weights(self):
        """Initialize network weights."""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.zeros_(m.bias)
            elif isinstance(m, (nn.Conv2d, nn.ConvTranspose2d)):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.zeros_(m.bias)
            elif isinstance(m, nn.BatchNorm2d):
                nn.init.ones_(m.weight)
                nn.init.zeros_(m.bias)


    def forward(self, x):
        """
        Forward pass of optimized SVDNet.

        Args:
            x: Input channel matrix [B, M, N, 2] or [M, N, 2]
        Returns:
            U: Left unitary matrix [B, M, R, 2] or [M, R, 2]
            S: Singular values [B, R] or [R]
            V: Right unitary matrix [B, N, R, 2] or [N, R, 2]
        """
        # Handle single sample input
        if x.ndim == 3:
            x = x.unsqueeze(0)
            squeeze_output = True
        else:
            squeeze_output = False

        if x.shape[-1] != 2:
            raise ValueError("Input last dimension must be 2 (real/imag)")

        batch_size = x.size(0)

        # Convert to NCHW format for CNN processing
        x = x.permute(0, 3, 1, 2)  # [B, 2, M, N]

        # Extract global features
        features = self.feature_extractor(x)  # [B, 256]

        # Shared feature processing
        shared_features = self.shared_fc(features)  # [B, 512]

        # Predict matrices and singular values
        U_flat = self.head_U(shared_features)  # [B, M*R*2]
        V_flat = self.head_V(shared_features)  # [B, N*R*2]
        S = self.head_S(shared_features)       # [B, R]

        # Reshape to matrix format
        U = U_flat.view(batch_size, self.dim, self.rank, 2)  # [B, M, R, 2]
        V = V_flat.view(batch_size, self.dim, self.rank, 2)  # [B, N, R, 2]

        # Apply orthogonality constraints
        U = apply_orthogonality_constraint(U, method=self.orthogonality_method)
        V = apply_orthogonality_constraint(V, method=self.orthogonality_method)

        # Remove batch dimension if input was single sample
        if squeeze_output:
            U = U.squeeze(0)
            V = V.squeeze(0)
            S = S.squeeze(0)

        return U, S, V

    def get_model_complexity(self):
        """Calculate model complexity metrics."""
        total_params = sum(p.numel() for p in self.parameters())
        trainable_params = sum(p.numel() for p in self.parameters() if p.requires_grad)

        return {
            'total_params': total_params,
            'trainable_params': trainable_params,
            'model_size_mb': total_params * 4 / (1024 * 1024)
        }


# Maintain compatibility with original SVDNet class name
class SVDNet(OptimizedSVDNet):
    """Compatibility wrapper for the original SVDNet interface."""

    def __init__(self, dim=64, rank=32, weight_path="svdnet.pth", **kwargs):
        # Map old parameters to new ones
        super().__init__(
            dim=dim,
            rank=rank,
            weight_path=weight_path,
            orthogonality_method='normalize'  # Default to simple normalization
        )


# -----------------------------------------------------------------------------
# Utility Functions
# -----------------------------------------------------------------------------

def _to_complex(mat: torch.Tensor) -> torch.Tensor:
    """Convert real-imag stacked tensor [..., 2] → complex."""
    return torch.complex(mat[..., 0], mat[..., 1])


def _to_ri(mat: torch.Tensor) -> torch.Tensor:
    """Convert complex tensor → real-imag stacked."""
    return torch.stack((mat.real, mat.imag), dim=-1)


# -----------------------------------------------------------------------------
# Alternative Transformer-based Architecture (Backup Solution)
# -----------------------------------------------------------------------------

class SVDTransformer(nn.Module):
    """
    Lightweight Transformer-based SVD approximation network.
    Alternative architecture for cases where CNN approach doesn't work well.
    """

    def __init__(self, dim=64, rank=32, d_model=256, nhead=8, num_layers=4):
        super().__init__()
        self.dim = dim
        self.rank = rank
        self.d_model = d_model

        # Input projection
        self.input_proj = nn.Linear(2, d_model)

        # Positional encoding
        self.pos_encoding = nn.Parameter(torch.randn(dim * dim, d_model))

        # Transformer encoder
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=nhead,
            dim_feedforward=d_model * 2,
            dropout=0.1,
            batch_first=True,
            activation='gelu'
        )
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers)

        # Output projection heads
        self.head_U = nn.Sequential(
            nn.Linear(d_model, dim * rank * 2),
            nn.Tanh()
        )
        self.head_V = nn.Sequential(
            nn.Linear(d_model, dim * rank * 2),
            nn.Tanh()
        )
        self.head_S = nn.Sequential(
            nn.Linear(d_model, rank),
            nn.Softplus()
        )

    def forward(self, x):
        """
        Args:
            x: Input channel matrix [B, M, N, 2] or [M, N, 2]
        Returns:
            U, S, V: SVD components
        """
        if x.ndim == 3:
            x = x.unsqueeze(0)
            squeeze_output = True
        else:
            squeeze_output = False

        batch_size, M, N, _ = x.shape

        # Flatten spatial dimensions
        x = x.view(batch_size, M * N, 2)  # [B, M*N, 2]

        # Project to model dimension
        x = self.input_proj(x)  # [B, M*N, d_model]

        # Add positional encoding
        x = x + self.pos_encoding.unsqueeze(0)

        # Transformer encoding
        x = self.transformer(x)  # [B, M*N, d_model]

        # Global pooling
        global_feat = torch.mean(x, dim=1)  # [B, d_model]

        # Predict matrices
        U_flat = self.head_U(global_feat)
        V_flat = self.head_V(global_feat)
        S = self.head_S(global_feat)

        # Reshape and apply constraints
        U = U_flat.view(batch_size, self.dim, self.rank, 2)
        V = V_flat.view(batch_size, self.dim, self.rank, 2)

        U = apply_orthogonality_constraint(U, method='normalize')
        V = apply_orthogonality_constraint(V, method='normalize')

        if squeeze_output:
            U = U.squeeze(0)
            V = V.squeeze(0)
            S = S.squeeze(0)

        return U, S, V